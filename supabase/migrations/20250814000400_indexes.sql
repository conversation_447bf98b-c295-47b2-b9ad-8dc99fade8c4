-- RatioHub Database Indexes
-- This migration creates performance indexes for the database

-- ============================================================================
-- PROFILES INDEXES
-- ============================================================================

CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_profiles_is_active ON public.profiles(is_active);
CREATE INDEX idx_profiles_created_at ON public.profiles(created_at);

-- ============================================================================
-- USER ROLES INDEXES
-- ============================================================================

CREATE INDEX idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX idx_user_roles_role ON public.user_roles(role);
CREATE INDEX idx_user_roles_assigned_at ON public.user_roles(assigned_at);

-- ============================================================================
-- TEAMS INDEXES
-- ============================================================================

CREATE INDEX idx_teams_created_by ON public.teams(created_by);
CREATE INDEX idx_teams_is_active ON public.teams(is_active);
CREATE INDEX idx_teams_created_at ON public.teams(created_at);

-- ============================================================================
-- TEAM MEMBERS INDEXES
-- ============================================================================

CREATE INDEX idx_team_members_team_id ON public.team_members(team_id);
CREATE INDEX idx_team_members_user_id ON public.team_members(user_id);
CREATE INDEX idx_team_members_joined_at ON public.team_members(joined_at);

-- ============================================================================
-- PROJECTS INDEXES
-- ============================================================================

CREATE INDEX idx_projects_project_id ON public.projects(project_id);
CREATE INDEX idx_projects_status ON public.projects(status);
CREATE INDEX idx_projects_created_by ON public.projects(created_by);
CREATE INDEX idx_projects_client_id ON public.projects(client_id);
CREATE INDEX idx_projects_team_id ON public.projects(team_id);
CREATE INDEX idx_projects_is_billable ON public.projects(is_billable);
CREATE INDEX idx_projects_start_date ON public.projects(start_date);
CREATE INDEX idx_projects_end_date ON public.projects(end_date);
CREATE INDEX idx_projects_created_at ON public.projects(created_at);

-- ============================================================================
-- PHASES INDEXES
-- ============================================================================

CREATE INDEX idx_phases_project_id ON public.phases(project_id);
CREATE INDEX idx_phases_status ON public.phases(status);
CREATE INDEX idx_phases_created_by ON public.phases(created_by);
CREATE INDEX idx_phases_order_index ON public.phases(order_index);
CREATE INDEX idx_phases_start_date ON public.phases(start_date);
CREATE INDEX idx_phases_end_date ON public.phases(end_date);
CREATE INDEX idx_phases_created_at ON public.phases(created_at);

-- ============================================================================
-- MILESTONES INDEXES
-- ============================================================================

CREATE INDEX idx_milestones_project_id ON public.milestones(project_id);
CREATE INDEX idx_milestones_phase_id ON public.milestones(phase_id);
CREATE INDEX idx_milestones_is_completed ON public.milestones(is_completed);
CREATE INDEX idx_milestones_due_date ON public.milestones(due_date);
CREATE INDEX idx_milestones_order_index ON public.milestones(order_index);
CREATE INDEX idx_milestones_created_at ON public.milestones(created_at);

-- ============================================================================
-- TASKS INDEXES
-- ============================================================================

CREATE INDEX idx_tasks_task_id ON public.tasks(task_id);
CREATE INDEX idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX idx_tasks_milestone_id ON public.tasks(milestone_id);
CREATE INDEX idx_tasks_parent_task_id ON public.tasks(parent_task_id);
CREATE INDEX idx_tasks_status ON public.tasks(status);
CREATE INDEX idx_tasks_priority ON public.tasks(priority);
CREATE INDEX idx_tasks_assigned_to ON public.tasks(assigned_to);
CREATE INDEX idx_tasks_created_by ON public.tasks(created_by);
CREATE INDEX idx_tasks_is_billable ON public.tasks(is_billable);
CREATE INDEX idx_tasks_due_date ON public.tasks(due_date);
CREATE INDEX idx_tasks_created_at ON public.tasks(created_at);
CREATE INDEX idx_tasks_tags ON public.tasks USING GIN(tags);

-- ============================================================================
-- TASK COMMENTS INDEXES
-- ============================================================================

CREATE INDEX idx_task_comments_task_id ON public.task_comments(task_id);
CREATE INDEX idx_task_comments_user_id ON public.task_comments(user_id);
CREATE INDEX idx_task_comments_created_at ON public.task_comments(created_at);

-- ============================================================================
-- TIME ENTRIES INDEXES
-- ============================================================================

CREATE INDEX idx_time_entries_user_id ON public.time_entries(user_id);
CREATE INDEX idx_time_entries_project_id ON public.time_entries(project_id);
CREATE INDEX idx_time_entries_task_id ON public.time_entries(task_id);
CREATE INDEX idx_time_entries_start_time ON public.time_entries(start_time);
CREATE INDEX idx_time_entries_end_time ON public.time_entries(end_time);
CREATE INDEX idx_time_entries_is_billable ON public.time_entries(is_billable);
CREATE INDEX idx_time_entries_created_at ON public.time_entries(created_at);

-- ============================================================================
-- FILE ATTACHMENTS INDEXES
-- ============================================================================

CREATE INDEX idx_file_attachments_project_id ON public.file_attachments(project_id);
CREATE INDEX idx_file_attachments_task_id ON public.file_attachments(task_id);
CREATE INDEX idx_file_attachments_uploaded_by ON public.file_attachments(uploaded_by);
CREATE INDEX idx_file_attachments_file_type ON public.file_attachments(file_type);
CREATE INDEX idx_file_attachments_created_at ON public.file_attachments(created_at);

-- ============================================================================
-- SUPPORT TICKETS INDEXES
-- ============================================================================

CREATE INDEX idx_support_tickets_status ON public.support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON public.support_tickets(priority);
CREATE INDEX idx_support_tickets_created_by ON public.support_tickets(created_by);
CREATE INDEX idx_support_tickets_assigned_to ON public.support_tickets(assigned_to);
CREATE INDEX idx_support_tickets_project_id ON public.support_tickets(project_id);
CREATE INDEX idx_support_tickets_created_at ON public.support_tickets(created_at);

-- ============================================================================
-- NOTIFICATIONS INDEXES
-- ============================================================================

CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_entity_type ON public.notifications(entity_type);
CREATE INDEX idx_notifications_entity_id ON public.notifications(entity_id);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at);

-- ============================================================================
-- PROJECT STATUS UPDATES INDEXES
-- ============================================================================

CREATE INDEX idx_project_status_updates_project_id ON public.project_status_updates(project_id);
CREATE INDEX idx_project_status_updates_user_id ON public.project_status_updates(user_id);
CREATE INDEX idx_project_status_updates_update_type ON public.project_status_updates(update_type);
CREATE INDEX idx_project_status_updates_created_at ON public.project_status_updates(created_at);

-- ============================================================================
-- ACTIVITY LOGS INDEXES
-- ============================================================================

CREATE INDEX idx_activity_logs_user_id ON public.activity_logs(user_id);
CREATE INDEX idx_activity_logs_entity_type ON public.activity_logs(entity_type);
CREATE INDEX idx_activity_logs_entity_id ON public.activity_logs(entity_id);
CREATE INDEX idx_activity_logs_action ON public.activity_logs(action);
CREATE INDEX idx_activity_logs_created_at ON public.activity_logs(created_at);

-- ============================================================================
-- PROJECT ACTIVITY LOGS INDEXES
-- ============================================================================

CREATE INDEX idx_project_activity_logs_project_id ON public.project_activity_logs(project_id);
CREATE INDEX idx_project_activity_logs_user_id ON public.project_activity_logs(user_id);
CREATE INDEX idx_project_activity_logs_action ON public.project_activity_logs(action);
CREATE INDEX idx_project_activity_logs_entity_type ON public.project_activity_logs(entity_type);
CREATE INDEX idx_project_activity_logs_entity_id ON public.project_activity_logs(entity_id);
CREATE INDEX idx_project_activity_logs_created_at ON public.project_activity_logs(created_at);

-- ============================================================================
-- SECURITY EVENTS INDEXES
-- ============================================================================

CREATE INDEX idx_security_events_event_type ON public.security_events(event_type);
CREATE INDEX idx_security_events_user_id ON public.security_events(user_id);
CREATE INDEX idx_security_events_ip_address ON public.security_events(ip_address);
CREATE INDEX idx_security_events_created_at ON public.security_events(created_at);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMMON QUERIES
-- ============================================================================

-- Projects by status and created_by
CREATE INDEX idx_projects_status_created_by ON public.projects(status, created_by);

-- Tasks by project and status
CREATE INDEX idx_tasks_project_status ON public.tasks(project_id, status);

-- Tasks by assignee and status
CREATE INDEX idx_tasks_assigned_status ON public.tasks(assigned_to, status);

-- Time entries by user and date range
CREATE INDEX idx_time_entries_user_date ON public.time_entries(user_id, start_time);

-- Time entries by project and date range
CREATE INDEX idx_time_entries_project_date ON public.time_entries(project_id, start_time);

-- Notifications by user and read status
CREATE INDEX idx_notifications_user_read ON public.notifications(user_id, is_read);

-- Support tickets by status and assigned user
CREATE INDEX idx_support_tickets_status_assigned ON public.support_tickets(status, assigned_to);
