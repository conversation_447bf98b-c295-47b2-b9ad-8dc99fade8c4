-- Create task_comments table for task discussions
CREATE TABLE IF NOT EXISTS public.task_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(user_id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create subtasks table (separate from main tasks)
CREATE TABLE IF NOT EXISTS public.subtasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    status task_status DEFAULT 'todo',
    assigned_to UUID REFERENCES public.profiles(user_id),
    due_date TIMESTAMP WITH TIME ZONE,
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP WITH TIME ZONE,
    completed_by UUID REFERENCES public.profiles(user_id),
    created_by UUID NOT NULL REFERENCES public.profiles(user_id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subtasks ENABLE ROW LEVEL SECURITY;

-- RLS policies for task_comments are created in the policies migration

-- Create RLS policies for subtasks
CREATE POLICY "Project stakeholders can manage subtasks" 
ON public.subtasks 
FOR ALL 
USING (
    is_admin(auth.uid()) OR 
    is_project_manager(auth.uid()) OR 
    assigned_to = auth.uid() OR 
    created_by = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.tasks t
        JOIN public.projects p ON p.id = t.project_id
        WHERE t.id = subtasks.parent_task_id
        AND (
            t.assigned_to = auth.uid() OR
            t.created_by = auth.uid() OR
            p.created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.team_members tm
                WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid()
            )
        )
    )
)
WITH CHECK (
    is_admin(auth.uid()) OR 
    is_project_manager(auth.uid()) OR 
    EXISTS (
        SELECT 1 FROM public.tasks t
        JOIN public.projects p ON p.id = t.project_id
        WHERE t.id = subtasks.parent_task_id
        AND (
            p.created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.team_members tm
                WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid()
            )
        )
    )
);

-- Triggers for updated_at columns are created in the triggers migration