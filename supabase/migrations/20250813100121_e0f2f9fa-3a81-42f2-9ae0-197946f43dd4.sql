-- Create SAP implementation teams and data
DO $$
DECLARE
    admin_user_id uuid := '183d5d61-4b48-4f60-b4a3-1cc45d76e713';
    consultant1_id uuid := gen_random_uuid();
    consultant2_id uuid := gen_random_uuid();
    consultant3_id uuid := gen_random_uuid();
    consultant4_id uuid := gen_random_uuid();
    architect_id uuid := gen_random_uuid();
    developer1_id uuid := gen_random_uuid();
    developer2_id uuid := gen_random_uuid();
    pm_id uuid := gen_random_uuid();
    
    sap_team_id uuid := gen_random_uuid();
    technical_team_id uuid := gen_random_uuid();
    
    project1_id uuid := gen_random_uuid();
    project2_id uuid := gen_random_uuid();
    project3_id uuid := gen_random_uuid();
BEGIN
    -- Create auth users first
    INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, instance_id, aud, role) VALUES
    (consultant1_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (consultant2_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (consultant3_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (consultant4_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (architect_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (developer1_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (developer2_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (pm_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated')
    ON CONFLICT (id) DO NOTHING;

    -- Create SAP team members profiles
    INSERT INTO public.profiles (user_id, first_name, last_name, email, position, department, is_active) VALUES
    (consultant1_id, 'Sarah', 'Williams', '<EMAIL>', 'Senior SAP Functional Consultant', 'SAP Practice', true),
    (consultant2_id, 'Michael', 'Chen', '<EMAIL>', 'SAP FI/CO Specialist', 'SAP Practice', true),
    (consultant3_id, 'Emily', 'Rodriguez', '<EMAIL>', 'SAP MM/SD Consultant', 'SAP Practice', true),
    (consultant4_id, 'David', 'Kumar', '<EMAIL>', 'SAP ABAP Developer', 'Technical Team', true),
    (architect_id, 'Jennifer', 'Thompson', '<EMAIL>', 'SAP Solution Architect', 'Architecture', true),
    (developer1_id, 'Alex', 'Martinez', '<EMAIL>', 'SAP Integration Developer', 'Technical Team', true),
    (developer2_id, 'Lisa', 'Anderson', '<EMAIL>', 'SAP Security Consultant', 'Security Team', true),
    (pm_id, 'Robert', 'Johnson', '<EMAIL>', 'SAP Program Manager', 'Project Management', true);

    -- Create user roles for team members
    INSERT INTO public.user_roles (user_id, role, assigned_by) VALUES
    (consultant1_id, 'user', admin_user_id),
    (consultant2_id, 'user', admin_user_id),
    (consultant3_id, 'user', admin_user_id),
    (consultant4_id, 'user', admin_user_id),
    (architect_id, 'project_manager', admin_user_id),
    (developer1_id, 'user', admin_user_id),
    (developer2_id, 'user', admin_user_id),
    (pm_id, 'project_manager', admin_user_id);

    -- Create SAP implementation teams
    INSERT INTO public.teams (id, name, description, created_by, is_active) VALUES
    (sap_team_id, 'SAP Functional Team', 'Core SAP functional consultants handling business process implementation', admin_user_id, true),
    (technical_team_id, 'SAP Technical Team', 'Technical specialists for SAP development, integration, and security', admin_user_id, true);

    -- Add team members
    INSERT INTO public.team_members (team_id, user_id, role) VALUES
    (sap_team_id, admin_user_id, 'lead'),
    (sap_team_id, consultant1_id, 'senior'),
    (sap_team_id, consultant2_id, 'member'),
    (sap_team_id, consultant3_id, 'member'),
    (sap_team_id, architect_id, 'lead'),
    (sap_team_id, pm_id, 'lead'),
    (technical_team_id, consultant4_id, 'senior'),
    (technical_team_id, developer1_id, 'member'),
    (technical_team_id, developer2_id, 'member'),
    (technical_team_id, architect_id, 'lead');

    -- Create SAP implementation projects
    INSERT INTO public.projects (id, project_id, name, description, status, created_by, team_id, start_date, end_date, budget, hourly_rate, currency, is_billable) VALUES
    (project1_id, 'SA001', 'Global Manufacturing SAP S/4HANA Implementation', 'End-to-end SAP S/4HANA implementation for global manufacturing company including FI, CO, MM, PP, SD modules with integration to existing systems', 'in_progress', admin_user_id, sap_team_id, '2024-01-15', '2025-06-30', 2500000.00, 250.00, 'USD', true),
    (project2_id, 'SA002', 'Retail Chain SAP Commerce Cloud Integration', 'SAP Commerce Cloud implementation with integration to SAP ERP for omnichannel retail operations', 'planning', admin_user_id, technical_team_id, '2024-03-01', '2024-12-15', 1200000.00, 275.00, 'USD', true),
    (project3_id, 'SA003', 'Financial Services SAP SuccessFactors HCM', 'SAP SuccessFactors Employee Central and Performance Management implementation for financial services company', 'in_progress', admin_user_id, sap_team_id, '2024-02-01', '2024-11-30', 850000.00, 225.00, 'USD', true);

    -- Create project phases for SAP S/4HANA project
    INSERT INTO public.phases (project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
    (project1_id, 'Project Initiation & Planning', 'Project setup, stakeholder alignment, and detailed planning', 'completed', '2024-01-15', '2024-02-29', 1, admin_user_id),
    (project1_id, 'Business Process Design', 'As-is analysis, to-be design, and gap analysis', 'completed', '2024-03-01', '2024-04-30', 2, admin_user_id),
    (project1_id, 'System Build & Configuration', 'SAP configuration, customization, and development', 'in_progress', '2024-05-01', '2024-09-30', 3, admin_user_id),
    (project1_id, 'Integration & Testing', 'System integration, unit testing, and user acceptance testing', 'planning', '2024-10-01', '2024-12-31', 4, admin_user_id),
    (project1_id, 'Deployment & Go-Live', 'Production deployment, go-live support, and hypercare', 'planning', '2025-01-01', '2025-03-31', 5, admin_user_id),
    (project1_id, 'Post Go-Live Support', 'Stabilization, optimization, and knowledge transfer', 'planning', '2025-04-01', '2025-06-30', 6, admin_user_id);

    -- Create milestones for SAP projects
    INSERT INTO public.milestones (project_id, name, description, due_date, is_completed, completed_at, completed_by) VALUES
    (project1_id, 'Project Charter Approval', 'Signed project charter and scope approval from steering committee', '2024-02-15', true, '2024-02-14', admin_user_id),
    (project1_id, 'Business Process Design Sign-off', 'Approved business process design documents for all modules', '2024-04-25', true, '2024-04-23', architect_id),
    (project1_id, 'Development Environment Setup', 'Complete SAP S/4HANA development system configuration', '2024-05-15', true, '2024-05-12', developer1_id),
    (project1_id, 'Core Module Configuration Complete', 'FI, CO, MM basic configuration completed', '2024-07-31', false, null, null),
    (project1_id, 'Integration Testing Complete', 'All system integrations tested and validated', '2024-11-30', false, null, null),
    (project1_id, 'Production Go-Live', 'Successful production deployment and go-live', '2025-02-15', false, null, null),
    
    (project2_id, 'Commerce Cloud Setup', 'SAP Commerce Cloud environment provisioned and configured', '2024-04-15', false, null, null),
    (project2_id, 'ERP Integration Complete', 'Integration between Commerce Cloud and SAP ERP operational', '2024-08-30', false, null, null),
    
    (project3_id, 'SuccessFactors Tenant Setup', 'Employee Central tenant configured with organizational structure', '2024-03-15', true, '2024-03-12', consultant1_id),
    (project3_id, 'Employee Data Migration', 'All employee data migrated and validated in SuccessFactors', '2024-06-30', false, null, null);

END $$;