-- Fix admin user email casing inconsistencies
DO $$
BEGIN
    -- Update any profiles with incorrect email casing
    UPDATE public.profiles 
    SET email = '<EMAIL>'
    WHERE email ILIKE '<EMAIL>' 
    AND email != '<EMAIL>';

    -- Update any references in team_members or other tables that might use the old casing
    -- This ensures consistency across the database
    
    -- Log the fix
    RAISE NOTICE 'Fixed admin user email casing to ensure consistency';
END $$;
