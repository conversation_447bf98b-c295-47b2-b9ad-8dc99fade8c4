-- Create SAP implementation data using admin user for all activities
DO $$
DECLARE
    admin_user_id uuid := '183d5d61-4b48-4f60-b4a3-1cc45d76e713';
    
    sap_team_id uuid := gen_random_uuid();
    technical_team_id uuid := gen_random_uuid();
    
    project1_id uuid := gen_random_uuid();
    project2_id uuid := gen_random_uuid();
    project3_id uuid := gen_random_uuid();
    
    phase1_id uuid := gen_random_uuid();
    phase2_id uuid := gen_random_uuid();
    phase3_id uuid := gen_random_uuid();
    phase4_id uuid := gen_random_uuid();
    phase5_id uuid := gen_random_uuid();
    phase6_id uuid := gen_random_uuid();
    
    milestone1_id uuid := gen_random_uuid();
    milestone2_id uuid := gen_random_uuid();
    milestone3_id uuid := gen_random_uuid();
    milestone4_id uuid := gen_random_uuid();
    milestone5_id uuid := gen_random_uuid();
    milestone6_id uuid := gen_random_uuid();
    milestone7_id uuid := gen_random_uuid();
    milestone8_id uuid := gen_random_uuid();
    milestone9_id uuid := gen_random_uuid();
    milestone10_id uuid := gen_random_uuid();
BEGIN
    -- Create SAP implementation teams
    INSERT INTO public.teams (id, name, description, created_by, is_active) VALUES
    (sap_team_id, 'SAP Functional Team', 'Core SAP functional consultants handling business process implementation', admin_user_id, true),
    (technical_team_id, 'SAP Technical Team', 'Technical specialists for SAP development, integration, and security', admin_user_id, true);

    -- Add admin user to teams
    INSERT INTO public.team_members (team_id, user_id, role) VALUES
    (sap_team_id, admin_user_id, 'lead'),
    (technical_team_id, admin_user_id, 'lead');

    -- Create SAP implementation projects
    INSERT INTO public.projects (id, project_id, name, description, status, created_by, team_id, start_date, end_date, budget, hourly_rate, currency, is_billable) VALUES
    (project1_id, 'SA001', 'Global Manufacturing SAP S/4HANA Implementation', 'End-to-end SAP S/4HANA implementation for global manufacturing company including FI, CO, MM, PP, SD modules with integration to existing systems', 'in_progress', admin_user_id, sap_team_id, '2024-01-15', '2025-06-30', 2500000.00, 250.00, 'USD', true),
    (project2_id, 'SA002', 'Retail Chain SAP Commerce Cloud Integration', 'SAP Commerce Cloud implementation with integration to SAP ERP for omnichannel retail operations', 'planning', admin_user_id, technical_team_id, '2024-03-01', '2024-12-15', 1200000.00, 275.00, 'USD', true),
    (project3_id, 'SA003', 'Financial Services SAP SuccessFactors HCM', 'SAP SuccessFactors Employee Central and Performance Management implementation for financial services company', 'in_progress', admin_user_id, sap_team_id, '2024-02-01', '2024-11-30', 850000.00, 225.00, 'USD', true)
    ON CONFLICT (project_id) DO NOTHING;

    -- Get the actual project IDs (in case they already existed)
    SELECT id INTO project1_id FROM public.projects WHERE project_id = 'SA001';
    SELECT id INTO project2_id FROM public.projects WHERE project_id = 'SA002';
    SELECT id INTO project3_id FROM public.projects WHERE project_id = 'SA003';

    -- Create project phases for SAP S/4HANA project
    INSERT INTO public.phases (id, project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
    (phase1_id, project1_id, 'Project Initiation & Planning', 'Project setup, stakeholder alignment, and detailed planning', 'completed', '2024-01-15', '2024-02-29', 1, admin_user_id),
    (phase2_id, project1_id, 'Business Process Design', 'As-is analysis, to-be design, and gap analysis', 'completed', '2024-03-01', '2024-04-30', 2, admin_user_id),
    (phase3_id, project1_id, 'System Build & Configuration', 'SAP configuration, customization, and development', 'in_progress', '2024-05-01', '2024-09-30', 3, admin_user_id),
    (phase4_id, project1_id, 'Integration & Testing', 'System integration, unit testing, and user acceptance testing', 'planning', '2024-10-01', '2024-12-31', 4, admin_user_id),
    (phase5_id, project1_id, 'Deployment & Go-Live', 'Production deployment, go-live support, and hypercare', 'planning', '2025-01-01', '2025-03-31', 5, admin_user_id),
    (phase6_id, project1_id, 'Post Go-Live Support', 'Stabilization, optimization, and knowledge transfer', 'planning', '2025-04-01', '2025-06-30', 6, admin_user_id);

    -- Create milestones for SAP projects
    INSERT INTO public.milestones (id, project_id, phase_id, name, description, due_date, is_completed, completed_at, completed_by) VALUES
    (milestone1_id, project1_id, phase1_id, 'Project Charter Approval', 'Signed project charter and scope approval from steering committee', '2024-02-15', true, '2024-02-14', admin_user_id),
    (milestone2_id, project1_id, phase2_id, 'Business Process Design Sign-off', 'Approved business process design documents for all modules', '2024-04-25', true, '2024-04-23', admin_user_id),
    (milestone3_id, project1_id, phase3_id, 'Development Environment Setup', 'Complete SAP S/4HANA development system configuration', '2024-05-15', true, '2024-05-12', admin_user_id),
    (milestone4_id, project1_id, phase3_id, 'Core Module Configuration Complete', 'FI, CO, MM basic configuration completed', '2024-07-31', false, null, null),
    (milestone5_id, project1_id, phase4_id, 'Integration Testing Complete', 'All system integrations tested and validated', '2024-11-30', false, null, null),
    (milestone6_id, project1_id, phase5_id, 'Production Go-Live', 'Successful production deployment and go-live', '2025-02-15', false, null, null),
    
    (milestone7_id, project2_id, null, 'Commerce Cloud Setup', 'SAP Commerce Cloud environment provisioned and configured', '2024-04-15', false, null, null),
    (milestone8_id, project2_id, null, 'ERP Integration Complete', 'Integration between Commerce Cloud and SAP ERP operational', '2024-08-30', false, null, null),
    
    (milestone9_id, project3_id, null, 'SuccessFactors Tenant Setup', 'Employee Central tenant configured with organizational structure', '2024-03-15', true, '2024-03-12', admin_user_id),
    (milestone10_id, project3_id, null, 'Employee Data Migration', 'All employee data migrated and validated in SuccessFactors', '2024-06-30', false, null, null);

END $$;