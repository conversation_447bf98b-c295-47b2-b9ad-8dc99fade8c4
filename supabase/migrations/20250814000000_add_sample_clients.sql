-- Add sample client users for testing project creation and fix admin user
DO $$
DECLARE
    client1_id uuid := gen_random_uuid();
    client2_id uuid := gen_random_uuid();
    client3_id uuid := gen_random_uuid();
    admin_user_id uuid := '183d5d61-4b48-4f60-b4a3-1cc45d76e713';
BEGIN
    -- Ensure admin user profile exists with correct email
    INSERT INTO public.profiles (user_id, first_name, last_name, email, position, department, is_active) VALUES
    (admin_user_id, 'Surendra', 'Ganne', '<EMAIL>', 'SAP Practice Director', 'Digital Transformation', true)
    ON CONFLICT (user_id) DO UPDATE SET
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        email = EXCLUDED.email,
        position = EXCLUDED.position,
        department = EXCLUDED.department;

    -- Ensure admin user has admin role
    INSERT INTO public.user_roles (user_id, role, assigned_by) VALUES
    (admin_user_id, 'admin', admin_user_id)
    ON CONFLICT (user_id, role) DO NOTHING;

    -- Create auth users for clients first
    INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, instance_id, aud, role) VALUES
    (client1_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (client2_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated'),
    (client3_id, '<EMAIL>', now(), now(), now(), '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated')
    ON CONFLICT (id) DO NOTHING;

    -- Create sample client profiles
    INSERT INTO public.profiles (user_id, first_name, last_name, email, position, department, is_active) VALUES
    (client1_id, 'John', 'Smith', '<EMAIL>', 'IT Director', 'Information Technology', true),
    (client2_id, 'Maria', 'Garcia', '<EMAIL>', 'Chief Technology Officer', 'Technology', true),
    (client3_id, 'David', 'Wilson', '<EMAIL>', 'VP of Operations', 'Operations', true);

    -- Assign client roles to these users
    INSERT INTO public.user_roles (user_id, role, assigned_by) VALUES
    (client1_id, 'client', admin_user_id),
    (client2_id, 'client', admin_user_id),
    (client3_id, 'client', admin_user_id);

    -- Update existing projects to have client assignments
    UPDATE public.projects 
    SET client_id = client1_id 
    WHERE project_id = 'SA001';

    UPDATE public.projects 
    SET client_id = client2_id 
    WHERE project_id = 'SA002';

    UPDATE public.projects 
    SET client_id = client3_id 
    WHERE project_id = 'SA003';

END $$;
