-- Create missing core tables for project management functionality

-- Create task_comments table for task discussions
CREATE TABLE IF NOT EXISTS public.task_comments (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    task_id UUID NOT NULL,
    user_id UUID NOT NULL,
    content TEXT NOT NULL,
    parent_comment_id UUID NULL, -- For threaded comments
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    is_deleted BOOLEAN DEFAULT false
);

-- Enable RLS on task_comments
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;

-- RLS policies for task_comments are created in the policies migration

-- Triggers for task_comments are created in the triggers migration

-- Create project_activity_logs table for project activity feed
CREATE TABLE public.project_activity_logs (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL,
    user_id UUID NOT NULL,
    activity_type TEXT NOT NULL,
    entity_type TEXT NOT NULL, -- 'task', 'milestone', 'project', 'comment', etc.
    entity_id UUID,
    description TEXT NOT NULL,
    metadata JSONB DEFAULT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on project_activity_logs
ALTER TABLE public.project_activity_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for project_activity_logs
CREATE POLICY "Project stakeholders can view activity logs" 
ON public.project_activity_logs 
FOR SELECT 
USING (
    is_admin(auth.uid()) OR 
    is_project_manager(auth.uid()) OR 
    (EXISTS (
        SELECT 1 FROM projects p 
        WHERE p.id = project_activity_logs.project_id 
        AND (
            p.created_by = auth.uid() OR 
            EXISTS (
                SELECT 1 FROM team_members tm 
                WHERE tm.team_id = p.team_id 
                AND tm.user_id = auth.uid()
            )
        )
    ))
);

CREATE POLICY "System can insert activity logs" 
ON public.project_activity_logs 
FOR INSERT 
WITH CHECK (true);

-- Create function to log project activities
CREATE OR REPLACE FUNCTION public.log_project_activity(
    p_project_id UUID,
    p_user_id UUID,
    p_activity_type TEXT,
    p_entity_type TEXT,
    p_entity_id UUID DEFAULT NULL,
    p_description TEXT DEFAULT '',
    p_metadata JSONB DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    INSERT INTO public.project_activity_logs (
        project_id,
        user_id,
        activity_type,
        entity_type,
        entity_id,
        description,
        metadata
    ) VALUES (
        p_project_id,
        p_user_id,
        p_activity_type,
        p_entity_type,
        p_entity_id,
        p_description,
        p_metadata
    );
END;
$$;

-- Create indexes for better performance
CREATE INDEX idx_task_comments_task_id ON public.task_comments(task_id);
CREATE INDEX idx_task_comments_user_id ON public.task_comments(user_id);
CREATE INDEX idx_task_comments_created_at ON public.task_comments(created_at DESC);
CREATE INDEX idx_project_activity_logs_project_id ON public.project_activity_logs(project_id);
CREATE INDEX idx_project_activity_logs_created_at ON public.project_activity_logs(created_at DESC);