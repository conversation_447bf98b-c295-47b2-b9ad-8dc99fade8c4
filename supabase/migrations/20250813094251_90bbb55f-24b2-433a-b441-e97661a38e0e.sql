-- Comprehensive sample data for the project management system
-- This creates realistic data across all tables to test functionality

-- First, let's get the current user for sample data
DO $$
DECLARE
    sample_user_id UUID;
    team1_id UUID;
    team2_id UUID;
    project1_id UUID;
    project2_id UUID;
    project3_id UUID;
    phase1_id UUID;
    phase2_id UUID;
    phase3_id UUID;
    phase4_id UUID;
    milestone1_id UUID;
    milestone2_id UUID;
    milestone3_id UUID;
    milestone4_id UUID;
    task1_id UUID;
    task2_id UUID;
    task3_id UUID;
    task4_id UUID;
BEGIN
    -- Get a sample user ID from existing data
    SELECT user_id INTO sample_user_id FROM public.profiles LIMIT 1;

    -- If no user exists, skip this migration
    IF sample_user_id IS NULL THEN
        RETURN;
    END IF;
    
    -- Create additional teams
    INSERT INTO public.teams (name, description, created_by, is_active)
    VALUES 
        ('Frontend Development Team', 'Responsible for UI/UX and frontend development', sample_user_id, true),
        ('Backend Development Team', 'Handles server-side development and APIs', sample_user_id, true),
        ('QA & Testing Team', 'Quality assurance and testing specialists', sample_user_id, true)
    RETURNING id INTO team1_id;
    
    -- Get the team IDs for the other teams
    SELECT id INTO team2_id FROM public.teams WHERE name = 'Backend Development Team';
    SELECT id INTO team1_id FROM public.teams WHERE name = 'Frontend Development Team';
    
    -- Add team members (using the same user for demo purposes)
    INSERT INTO public.team_members (team_id, user_id, role)
    VALUES 
        (team1_id, sample_user_id, 'lead'),
        (team2_id, sample_user_id, 'member')
    ON CONFLICT (team_id, user_id) DO NOTHING;
    
    -- Create additional projects
    INSERT INTO public.projects (name, description, status, created_by, team_id, start_date, end_date, budget, currency, is_billable, hourly_rate)
    VALUES 
        ('E-Commerce Platform', 'Modern e-commerce platform with advanced features', 'in_progress', sample_user_id, team1_id, CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE + INTERVAL '60 days', 75000, 'USD', true, 85),
        ('Mobile App Development', 'Cross-platform mobile application', 'planning', sample_user_id, team2_id, CURRENT_DATE + INTERVAL '15 days', CURRENT_DATE + INTERVAL '120 days', 50000, 'USD', true, 75),
        ('Data Analytics Dashboard', 'Business intelligence and analytics platform', 'completed', sample_user_id, team1_id, CURRENT_DATE - INTERVAL '90 days', CURRENT_DATE - INTERVAL '10 days', 40000, 'USD', true, 90)
    RETURNING id INTO project1_id;
    
    -- Get project IDs
    SELECT id INTO project2_id FROM public.projects WHERE name = 'Mobile App Development';
    SELECT id INTO project3_id FROM public.projects WHERE name = 'Data Analytics Dashboard';
    
    -- Create phases for the e-commerce project
    INSERT INTO public.phases (project_id, name, description, status, start_date, end_date, order_index, created_by)
    VALUES 
        (project1_id, 'Research & Discovery', 'Market research and requirement gathering', 'completed', CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE - INTERVAL '20 days', 1, sample_user_id),
        (project1_id, 'Design & Prototyping', 'UI/UX design and interactive prototypes', 'in_progress', CURRENT_DATE - INTERVAL '20 days', CURRENT_DATE + INTERVAL '10 days', 2, sample_user_id),
        (project1_id, 'Development', 'Core development and implementation', 'planning', CURRENT_DATE + INTERVAL '10 days', CURRENT_DATE + INTERVAL '50 days', 3, sample_user_id)
    RETURNING id INTO phase1_id;
    
    -- Get phase IDs
    SELECT id INTO phase2_id FROM public.phases WHERE name = 'Design & Prototyping' AND project_id = project1_id;
    SELECT id INTO phase3_id FROM public.phases WHERE name = 'Development' AND project_id = project1_id;
    
    -- Create phases for mobile app project
    INSERT INTO public.phases (project_id, name, description, status, start_date, end_date, order_index, created_by)
    VALUES 
        (project2_id, 'Planning & Architecture', 'Technical planning and app architecture', 'planning', CURRENT_DATE + INTERVAL '15 days', CURRENT_DATE + INTERVAL '30 days', 1, sample_user_id)
    RETURNING id INTO phase4_id;
    
    -- Create milestones
    INSERT INTO public.milestones (project_id, phase_id, name, description, due_date, is_completed, completed_at, completed_by)
    VALUES 
        (project1_id, phase1_id, 'Market Research Complete', 'Complete analysis of target market and competitors', CURRENT_DATE - INTERVAL '25 days', true, CURRENT_DATE - INTERVAL '25 days', sample_user_id),
        (project1_id, phase2_id, 'Design System Ready', 'Complete design system and component library', CURRENT_DATE + INTERVAL '5 days', false, NULL, NULL),
        (project1_id, phase3_id, 'MVP Development', 'Minimum viable product ready for testing', CURRENT_DATE + INTERVAL '40 days', false, NULL, NULL),
        (project2_id, phase4_id, 'Technical Specifications', 'Complete technical architecture documentation', CURRENT_DATE + INTERVAL '25 days', false, NULL, NULL)
    RETURNING id INTO milestone1_id;
    
    -- Get milestone IDs
    SELECT id INTO milestone2_id FROM public.milestones WHERE name = 'Design System Ready';
    SELECT id INTO milestone3_id FROM public.milestones WHERE name = 'MVP Development';
    SELECT id INTO milestone4_id FROM public.milestones WHERE name = 'Technical Specifications';
    
    -- Create tasks
    INSERT INTO public.tasks (project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable, tags)
    VALUES 
        (project1_id, milestone1_id, 'EC001-T001', 'Competitor Analysis', 'Analyze top 5 competitors in the e-commerce space', 'completed', 'high', sample_user_id, sample_user_id, CURRENT_DATE - INTERVAL '28 days', 16, 18, true, ARRAY['research', 'analysis']),
        (project1_id, milestone1_id, 'EC001-T002', 'User Persona Development', 'Create detailed user personas based on research', 'completed', 'medium', sample_user_id, sample_user_id, CURRENT_DATE - INTERVAL '26 days', 12, 14, true, ARRAY['research', 'ux']),
        (project1_id, milestone2_id, 'EC001-T003', 'Wireframe Creation', 'Create wireframes for all major pages', 'in_progress', 'high', sample_user_id, sample_user_id, CURRENT_DATE + INTERVAL '3 days', 24, 8, true, ARRAY['design', 'wireframes']),
        (project1_id, milestone2_id, 'EC001-T004', 'Component Library', 'Build reusable UI component library', 'todo', 'medium', sample_user_id, sample_user_id, CURRENT_DATE + INTERVAL '7 days', 32, 0, true, ARRAY['design', 'components']),
        (project1_id, milestone3_id, 'EC001-T005', 'Database Setup', 'Set up production database and schemas', 'todo', 'high', sample_user_id, sample_user_id, CURRENT_DATE + INTERVAL '15 days', 8, 0, true, ARRAY['backend', 'database']),
        (project2_id, milestone4_id, 'MA001-T001', 'Technology Stack Selection', 'Choose appropriate technology stack for mobile app', 'todo', 'high', sample_user_id, sample_user_id, CURRENT_DATE + INTERVAL '20 days', 16, 0, true, ARRAY['planning', 'technology'])
    RETURNING id INTO task1_id;
    
    -- Get task IDs for subtasks
    SELECT id INTO task2_id FROM public.tasks WHERE task_id = 'EC001-T003';
    SELECT id INTO task3_id FROM public.tasks WHERE task_id = 'EC001-T004';
    SELECT id INTO task4_id FROM public.tasks WHERE task_id = 'EC001-T005';
    
    -- Create subtasks
    INSERT INTO public.tasks (project_id, milestone_id, parent_task_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable, tags)
    VALUES 
        (project1_id, milestone2_id, task2_id, 'EC001-T003-S001', 'Homepage Wireframe', 'Create detailed wireframe for homepage', 'completed', 'medium', sample_user_id, sample_user_id, CURRENT_DATE - INTERVAL '1 day', 4, 5, true, ARRAY['design', 'homepage']),
        (project1_id, milestone2_id, task2_id, 'EC001-T003-S002', 'Product Page Wireframe', 'Create wireframe for product detail pages', 'in_progress', 'medium', sample_user_id, sample_user_id, CURRENT_DATE + INTERVAL '2 days', 4, 2, true, ARRAY['design', 'product']),
        (project1_id, milestone2_id, task3_id, 'EC001-T004-S001', 'Button Components', 'Create button component variations', 'todo', 'low', sample_user_id, sample_user_id, CURRENT_DATE + INTERVAL '5 days', 6, 0, true, ARRAY['components', 'buttons']),
        (project1_id, milestone3_id, task4_id, 'EC001-T005-S001', 'User Authentication Schema', 'Design and implement user auth tables', 'todo', 'high', sample_user_id, sample_user_id, CURRENT_DATE + INTERVAL '12 days', 4, 0, true, ARRAY['database', 'auth']);
    
    -- Create time entries
    INSERT INTO public.time_entries (user_id, project_id, task_id, description, start_time, end_time, duration_minutes, hourly_rate, is_billable)
    VALUES 
        (sample_user_id, project1_id, task1_id, 'Researching competitor features and pricing', CURRENT_DATE - INTERVAL '28 days' + TIME '09:00', CURRENT_DATE - INTERVAL '28 days' + TIME '17:00', 480, 85, true),
        (sample_user_id, project1_id, task1_id, 'Completing competitor analysis report', CURRENT_DATE - INTERVAL '27 days' + TIME '09:00', CURRENT_DATE - INTERVAL '27 days' + TIME '13:00', 240, 85, true),
        (sample_user_id, project1_id, task2_id, 'Creating wireframes for homepage', CURRENT_DATE - INTERVAL '2 days' + TIME '10:00', CURRENT_DATE - INTERVAL '2 days' + TIME '15:00', 300, 85, true),
        (sample_user_id, project1_id, task2_id, 'Wireframe revisions based on feedback', CURRENT_DATE - INTERVAL '1 day' + TIME '14:00', CURRENT_DATE - INTERVAL '1 day' + TIME '16:00', 120, 85, true);
    
    -- Create task comments
    INSERT INTO public.task_comments (task_id, user_id, content)
    VALUES 
        (task1_id, sample_user_id, 'Completed the initial competitor analysis. Found some interesting features we should consider implementing.'),
        (task2_id, sample_user_id, 'Working on the wireframes now. The homepage layout is looking good so far.'),
        (task2_id, sample_user_id, 'Added mobile responsiveness considerations to the wireframe design.'),
        (task3_id, sample_user_id, 'Need to ensure the component library follows our design system guidelines.');
    
    -- Create file attachments
    INSERT INTO public.file_attachments (project_id, task_id, file_name, file_url, file_type, file_size, uploaded_by)
    VALUES 
        (project1_id, task1_id, 'competitor-analysis-report.pdf', '/project-files/ec001/competitor-analysis-report.pdf', 'application/pdf', 2048576, sample_user_id),
        (project1_id, task2_id, 'homepage-wireframe-v1.png', '/project-files/ec001/homepage-wireframe-v1.png', 'image/png', 1024000, sample_user_id),
        (project1_id, task2_id, 'user-flow-diagram.pdf', '/project-files/ec001/user-flow-diagram.pdf', 'application/pdf', 1536000, sample_user_id),
        (project1_id, NULL, 'project-requirements.docx', '/project-files/ec001/project-requirements.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 512000, sample_user_id);
    
    -- Create support tickets
    INSERT INTO public.support_tickets (title, description, priority, status, created_by, assigned_to, project_id)
    VALUES 
        ('Performance Issues on Homepage', 'Homepage is loading slowly, need to investigate and optimize', 'high', 'in_progress', sample_user_id, sample_user_id, project1_id),
        ('Feature Request: Advanced Search', 'Client requested advanced search functionality with filters', 'medium', 'open', sample_user_id, NULL, project1_id),
        ('Bug: Mobile Navigation Not Working', 'Mobile navigation menu is not responsive on certain devices', 'high', 'open', sample_user_id, sample_user_id, project1_id);
    
    -- Create project activity logs using the function
    PERFORM public.log_project_activity(
        project1_id,
        sample_user_id,
        'task_created',
        'task',
        task1_id,
        'Created task: Competitor Analysis',
        jsonb_build_object('task_title', 'Competitor Analysis', 'priority', 'high')
    );
    
    PERFORM public.log_project_activity(
        project1_id,
        sample_user_id,
        'task_completed',
        'task',
        task1_id,
        'Completed task: Competitor Analysis',
        jsonb_build_object('task_title', 'Competitor Analysis', 'completion_time', '18 hours')
    );
    
    PERFORM public.log_project_activity(
        project1_id,
        sample_user_id,
        'milestone_completed',
        'milestone',
        milestone1_id,
        'Completed milestone: Market Research Complete',
        jsonb_build_object('milestone_name', 'Market Research Complete', 'tasks_completed', 2)
    );
    
    PERFORM public.log_project_activity(
        project1_id,
        sample_user_id,
        'file_uploaded',
        'file',
        NULL,
        'Uploaded file: competitor-analysis-report.pdf',
        jsonb_build_object('file_name', 'competitor-analysis-report.pdf', 'file_size', '2MB')
    );
    
END $$;