-- RatioHub Row Level Security Policies
-- This migration enables RLS and creates comprehensive security policies

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.phases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.milestones ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_status_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- PROFILES POLICIES
-- ============================================================================

CREATE POLICY "Users can view own profile or admins can view all" 
ON public.profiles FOR SELECT 
USING ((auth.uid() = user_id) OR is_admin(auth.uid()));

CREATE POLICY "Users can create only their own profile" 
ON public.profiles FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update only their own profile" 
ON public.profiles FOR UPDATE 
USING (auth.uid() = user_id) 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Only admins can delete profiles" 
ON public.profiles FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- USER ROLES POLICIES
-- ============================================================================

CREATE POLICY "Users can view their own roles, admins can view all" 
ON public.user_roles FOR SELECT 
USING ((auth.uid() = user_id) OR is_admin(auth.uid()));

CREATE POLICY "Only admins can manage user roles" 
ON public.user_roles FOR INSERT 
WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can update user roles" 
ON public.user_roles FOR UPDATE 
USING (is_admin(auth.uid())) 
WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can delete user roles" 
ON public.user_roles FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- TEAMS POLICIES
-- ============================================================================

CREATE POLICY "Team members and managers can view teams" 
ON public.teams FOR SELECT 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = teams.id AND tm.user_id = auth.uid())
);

CREATE POLICY "Admins and project managers can create teams" 
ON public.teams FOR INSERT 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Team creators and admins can update teams" 
ON public.teams FOR UPDATE 
USING (
  is_admin(auth.uid()) OR 
  (is_project_manager(auth.uid()) AND created_by = auth.uid())
) 
WITH CHECK (
  is_admin(auth.uid()) OR 
  (is_project_manager(auth.uid()) AND created_by = auth.uid())
);

CREATE POLICY "Only admins can delete teams" 
ON public.teams FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- TEAM MEMBERS POLICIES
-- ============================================================================

CREATE POLICY "Team members can view team membership" 
ON public.team_members FOR SELECT 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid() OR 
  EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = team_members.team_id AND tm.user_id = auth.uid())
);

CREATE POLICY "Admins and project managers can manage team members" 
ON public.team_members FOR INSERT 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Admins and project managers can update team members" 
ON public.team_members FOR UPDATE 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Admins and project managers can remove team members" 
ON public.team_members FOR DELETE 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

-- ============================================================================
-- PROJECTS POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can view projects" 
ON public.projects FOR SELECT 
USING (has_project_access(auth.uid(), projects.id));

CREATE POLICY "Admins and project managers can create projects" 
ON public.projects FOR INSERT 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Project stakeholders can update projects" 
ON public.projects FOR UPDATE 
USING (has_project_access(auth.uid(), projects.id)) 
WITH CHECK (has_project_access(auth.uid(), projects.id));

CREATE POLICY "Only admins can delete projects" 
ON public.projects FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- PHASES POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can manage phases" 
ON public.phases FOR ALL 
USING (has_project_access(auth.uid(), phases.project_id)) 
WITH CHECK (has_project_access(auth.uid(), phases.project_id));

-- ============================================================================
-- MILESTONES POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can manage milestones" 
ON public.milestones FOR ALL 
USING (has_project_access(auth.uid(), milestones.project_id)) 
WITH CHECK (has_project_access(auth.uid(), milestones.project_id));

-- ============================================================================
-- TASKS POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can manage tasks" 
ON public.tasks FOR ALL 
USING (
  has_project_access(auth.uid(), tasks.project_id) OR 
  assigned_to = auth.uid() OR 
  created_by = auth.uid()
) 
WITH CHECK (
  has_project_access(auth.uid(), tasks.project_id) OR 
  assigned_to = auth.uid() OR 
  created_by = auth.uid()
);

-- ============================================================================
-- TASK COMMENTS POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can manage task comments" 
ON public.task_comments FOR ALL 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM tasks t 
    WHERE t.id = task_comments.task_id 
    AND has_project_access(auth.uid(), t.project_id)
  )
) 
WITH CHECK (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid()
);

-- ============================================================================
-- TIME ENTRIES POLICIES
-- ============================================================================

CREATE POLICY "Users can manage their own time entries" 
ON public.time_entries FOR ALL 
USING (
  user_id = auth.uid() OR 
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid())
) 
WITH CHECK (
  user_id = auth.uid() OR 
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid())
);

-- ============================================================================
-- FILE ATTACHMENTS POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can manage file attachments" 
ON public.file_attachments FOR ALL 
USING (
  uploaded_by = auth.uid() OR 
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  (project_id IS NOT NULL AND has_project_access(auth.uid(), project_id))
) 
WITH CHECK (
  uploaded_by = auth.uid() OR 
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid())
);

-- ============================================================================
-- SUPPORT TICKETS POLICIES
-- ============================================================================

CREATE POLICY "Users can manage their own tickets, admins can manage all" 
ON public.support_tickets FOR ALL 
USING (
  created_by = auth.uid() OR 
  assigned_to = auth.uid() OR 
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid())
) 
WITH CHECK (
  created_by = auth.uid() OR 
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid())
);

-- ============================================================================
-- NOTIFICATIONS POLICIES
-- ============================================================================

CREATE POLICY "Users can manage their own notifications" 
ON public.notifications FOR ALL 
USING (user_id = auth.uid()) 
WITH CHECK (user_id = auth.uid());

-- ============================================================================
-- PROJECT STATUS UPDATES POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can view project updates" 
ON public.project_status_updates FOR SELECT 
USING (has_project_access(auth.uid(), project_status_updates.project_id));

CREATE POLICY "Project stakeholders can create project updates" 
ON public.project_status_updates FOR INSERT 
WITH CHECK (has_project_access(auth.uid(), project_status_updates.project_id));

-- ============================================================================
-- ACTIVITY LOGS POLICIES
-- ============================================================================

CREATE POLICY "Admins can view all activity logs" 
ON public.activity_logs FOR SELECT 
USING (is_admin(auth.uid()));

-- ============================================================================
-- PROJECT ACTIVITY LOGS POLICIES
-- ============================================================================

CREATE POLICY "Project stakeholders can view project activity logs" 
ON public.project_activity_logs FOR SELECT 
USING (has_project_access(auth.uid(), project_activity_logs.project_id));

-- ============================================================================
-- SECURITY EVENTS POLICIES
-- ============================================================================

CREATE POLICY "Only admins can view security events" 
ON public.security_events FOR SELECT 
USING (is_admin(auth.uid()));
