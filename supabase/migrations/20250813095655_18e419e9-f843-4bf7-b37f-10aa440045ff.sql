-- Create a simple admin user for testing
-- First, let's create an admin bypass for teams view
CREATE POLICY "Allow authenticated users to view teams" 
ON public.teams 
FOR SELECT 
USING (auth.uid() IS NOT NULL);

-- Allow authenticated users to view support tickets
CREATE POLICY "Allow authenticated users to view support tickets" 
ON public.support_tickets 
FOR SELECT 
USING (auth.uid() IS NOT NULL);

-- Allow viewing profiles for authenticated users  
CREATE POLICY "Allow authenticated users to view profiles" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() IS NOT NULL);

-- Insert a test admin user in auth.users first
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at, instance_id, aud, role)
VALUES (
  '183d5d61-4b48-4f60-b4a3-1cc45d76e713',
  '<EMAIL>',
  now(),
  now(),
  now(),
  '00000000-0000-0000-0000-000000000000',
  'authenticated',
  'authenticated'
) ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  updated_at = now();

-- Insert a test admin user profile and role
INSERT INTO public.profiles (user_id, first_name, last_name, email, is_active)
VALUES ('183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'Surendra', 'Ganne', '<EMAIL>', true)
ON CONFLICT (user_id) DO UPDATE SET
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  email = EXCLUDED.email;

-- Give this user admin role
INSERT INTO public.user_roles (user_id, role, assigned_by)
VALUES ('183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'admin', '183d5d61-4b48-4f60-b4a3-1cc45d76e713')
ON CONFLICT (user_id, role) DO NOTHING;